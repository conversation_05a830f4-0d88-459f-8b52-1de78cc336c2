{"master": {"tasks": [{"id": 1, "title": "Setup Project Repository and Initial Scaffolding", "description": "Initialize Laravel 11.x project with PHP 8.2+, MySQL 8.0, and required dependencies. Configure basic CI/CD pipeline and project structure.", "details": "Create new Laravel project, set up MySQL database, install required PHP extensions, initialize Git repository, configure .env, and set up basic CI/CD (e.g., GitHub Actions). Scaffold initial directory structure for MVC and API routes.", "testStrategy": "Verify Laravel installation, database connection, and basic routing. Run initial test suite (phpunit).", "priority": "high", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 2, "title": "Design and Implement Core Database Schema", "description": "Define and implement database schema for gardens, plants, categories, users, QR labels, and analytics.", "details": "Create migrations for gardens, plants, plant_categories, users, qr_labels, analytics. Define relationships: gardens have many plants, plants belong to many categories, users belong to gardens, analytics track plant interactions. Use Laravel migrations and seeders.", "testStrategy": "Test migrations and seeders, verify relationships, check data integrity with sample data.", "priority": "high", "dependencies": [1], "status": "pending", "subtasks": []}, {"id": 3, "title": "Implement User Authentication and Basic Roles", "description": "Set up user authentication (login/logout) and basic role management (admin/editor).", "details": "Use Laravel Breeze or Fortify for authentication. Add roles (admin, editor) using Laravel gates or policies. Implement basic user management UI.", "testStrategy": "Test user registration, login, logout, and role-based access. Verify admin/editor permissions.", "priority": "high", "dependencies": [1, 2], "status": "pending", "subtasks": []}, {"id": 4, "title": "Develop Plant CRUD Operations", "description": "Implement create, read, update, delete for plant records with basic fields (name, scientific name, description).", "details": "Create Plant model, controller, and views. Implement CRUD endpoints. Use Laravel validation. Add basic admin interface for plant management.", "testStrategy": "Test all CRUD operations via UI and API. Validate input and output.", "priority": "high", "dependencies": [2, 3], "status": "pending", "subtasks": []}, {"id": 5, "title": "Implement QR Code Generation and Download", "description": "Generate unique QR codes for each plant and provide download functionality.", "details": "Integrate Simple QR Code library. Add QR code generation endpoint. Store QR code images in file-based storage. Provide download link in admin interface.", "testStrategy": "Test QR code generation for each plant, verify download and image quality. Ensure QR codes scan correctly.", "priority": "high", "dependencies": [4], "status": "pending", "subtasks": []}, {"id": 6, "title": "Build Public Plant Detail Pages", "description": "Create responsive plant detail pages accessible via QR code links.", "details": "Develop Blade templates for plant detail pages. Use Tailwind CSS for responsive design. Display plant name, scientific name, description, and single image.", "testStrategy": "Test page responsiveness, content display, and QR code redirection. Verify on mobile and desktop.", "priority": "high", "dependencies": [4, 5], "status": "pending", "subtasks": []}, {"id": 7, "title": "Implement File Upload for Plant Images", "description": "Enable single image upload per plant in admin interface.", "details": "Add file upload field to plant form. Use Laravel file storage. Display uploaded image on plant detail page. Validate file type and size.", "testStrategy": "Test image upload, display, and validation. Verify image appears on plant detail page.", "priority": "medium", "dependencies": [4], "status": "pending", "subtasks": []}, {"id": 8, "title": "Develop Basic Admin Dashboard", "description": "Create admin dashboard for plant management and analytics overview.", "details": "Build dashboard view with plant list, quick actions, and basic analytics. Use Livewire for real-time updates. Secure with admin role.", "testStrategy": "Test dashboard access, plant list, and analytics display. Verify role-based security.", "priority": "medium", "dependencies": [3, 4], "status": "pending", "subtasks": []}, {"id": 9, "title": "Enhance Plant Content with Multiple Images and Rich Text", "description": "Allow multiple image uploads and rich text editing for plant descriptions.", "details": "Extend plant model for multiple images. Add gallery UI. Integrate Trix or similar rich text editor. Update plant detail page to display gallery and formatted text.", "testStrategy": "Test multiple image upload, gallery display, and rich text editing. Verify content persistence.", "priority": "medium", "dependencies": [4, 7], "status": "pending", "subtasks": []}, {"id": 10, "title": "Implement Plant Categories and Tagging", "description": "Add category management and tagging system for plants.", "details": "Create category model and pivot table. Add category assignment UI. Allow filtering by category in admin and public views.", "testStrategy": "Test category creation, assignment, and filtering. Verify UI updates.", "priority": "medium", "dependencies": [2, 4], "status": "pending", "subtasks": []}, {"id": 11, "title": "Develop Interactive Garden Mapping", "description": "Implement digital garden map with plant locations and GPS integration.", "details": "Add GPS coordinates to plant model. Use Leaflet or similar for interactive map. Display plant locations and visitor position. Integrate with public plant detail pages.", "testStrategy": "Test map display, plant location markers, and GPS integration. Verify on mobile devices.", "priority": "medium", "dependencies": [4, 6], "status": "pending", "subtasks": []}, {"id": 12, "title": "Add Social Sharing and Related Plants", "description": "Enable social sharing for plant pages and suggest related plants.", "details": "Add social sharing buttons (Facebook, Twitter). Implement related plants logic based on categories. Display related plants on detail page.", "testStrategy": "Test social sharing links and related plant suggestions. Verify UI and logic.", "priority": "low", "dependencies": [6, 10], "status": "pending", "subtasks": []}, {"id": 13, "title": "Implement Analytics and Visitor Tracking", "description": "Track visitor interactions and provide analytics dashboard.", "details": "Add analytics model for tracking plant views. Create analytics dashboard with charts. Use Laravel Queue for background processing if needed.", "testStrategy": "Test tracking of plant views, analytics dashboard display, and data accuracy.", "priority": "medium", "dependencies": [2, 8], "status": "pending", "subtasks": []}, {"id": 14, "title": "Build Progressive Web App (PWA) Features", "description": "Enable offline access and installable app experience.", "details": "Add PWA manifest and service worker. Cache essential plant data for offline access. Test on mobile devices.", "testStrategy": "Test offline access, app installation, and cached content. Verify on iOS and Android.", "priority": "medium", "dependencies": [6, 9], "status": "pending", "subtasks": []}, {"id": 15, "title": "Optimize Performance and Accessibility", "description": "Optimize for mobile performance, low bandwidth, and accessibility compliance.", "details": "Implement image optimization (<200KB per photo), aggressive caching, high-contrast design, screen reader support, keyboard navigation. Use Laravel caching and Tailwind utilities.", "testStrategy": "Test page load times, image sizes, accessibility features, and mobile usability. Verify WCAG 2.1 AA compliance.", "priority": "medium", "dependencies": [6, 9, 14], "status": "pending", "subtasks": []}], "metadata": {"created": "2025-06-17T13:17:07.847Z", "updated": "2025-06-17T13:17:07.847Z", "description": "Tasks for master context"}}}